import os

from dotenv import load_dotenv

load_dotenv()

API_KEY = os.getenv("API_KEY")
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
OPENAI_EMBEDDING_MODEL_ID = "text-embedding-3-small"
OPENAI_MODEL_ID = "gpt-4o-mini"
CACHE_DIR = os.getenv("CACHE_DIR")

# Celery Configuration
REDIS_URL = os.getenv("REDIS_URL")
CELERY_BROKER_URL = os.getenv("CELERY_BROKER_URL", REDIS_URL)
CELERY_RESULT_BACKEND = os.getenv("CELERY_RESULT_BACKEND", REDIS_URL)

# Database Configuration
DATABASE_URL = os.getenv("DATABASE_URL")
