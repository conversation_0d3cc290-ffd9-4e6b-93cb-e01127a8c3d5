import json

from langchain_core.documents import Document

from rebeldotaichallenge.database import default_collection


def add_default_data():
    with open(".data/rag_data/data.json", "r") as f:
        data = json.load(f)

    default_collection.add_documents(
        [
            Document(page_content=item["question"], metadata={"answer": item["answer"]})
            for item in data["data"]
        ]
    )


if __name__ == "__main__":
    add_default_data()
    print(default_collection.get_collection_stats("default"))
    print(
        default_collection.similarity_search_with_score(
            "How do I change my profile information?", k=5
        )
    )
