from rebeldotaichallenge.database.pg_vector import LangChainPGVectorStore

default_collection = LangChainPGVectorStore(
    connection_string="postgresql+psycopg://langchain:langchain@localhost:6024/langchain",
    collection_name="default",
    pre_delete_collection=False,
)
extended_collection = LangChainPGVectorStore(
    connection_string="postgresql+psycopg://langchain:langchain@localhost:6024/langchain",
    collection_name="extended",
    pre_delete_collection=False,
)
